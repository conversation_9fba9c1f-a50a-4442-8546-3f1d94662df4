#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传功能测试脚本
"""

import os
import tempfile
from file_upload import FileUploader


def create_test_file():
    """创建一个测试文件"""
    # 创建data目录
    os.makedirs("data", exist_ok=True)
    
    # 创建一个简单的测试文件
    test_content = """这是一个测试文档
用于验证文件上传功能

文件内容包含：
- 中文字符
- 英文字符 English
- 数字 123456
- 特殊符号 !@#$%^&*()

测试时间: 2024-01-01
"""
    
    test_file_path = "data/test_document.txt"
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print(f"已创建测试文件: {test_file_path}")
    return test_file_path


def test_md5_calculation():
    """测试MD5计算功能"""
    print("\n=== 测试MD5计算 ===")
    
    test_file = create_test_file()
    uploader = FileUploader()
    
    md5_value = uploader.calculate_md5(test_file)
    file_size = uploader.get_file_size(test_file)
    content_type = uploader.get_content_type(test_file)
    
    print(f"文件: {test_file}")
    print(f"MD5: {md5_value}")
    print(f"大小: {file_size} bytes")
    print(f"类型: {content_type}")


def test_api_request_format():
    """测试API请求格式"""
    print("\n=== 测试API请求格式 ===")
    
    test_file = create_test_file()
    uploader = FileUploader()
    
    # 模拟构建请求数据
    file_name = os.path.basename(test_file)
    file_size = uploader.get_file_size(test_file)
    content_md5 = uploader.calculate_md5(test_file)
    content_type = uploader.get_content_type(test_file)
    
    request_data = {
        "contentMd5": content_md5,
        "contentType": content_type,
        "convertToPDF": False,
        "convertToHTML": False,
        "fileName": file_name,
        "fileSize": file_size,
        "convertToOFD": True
    }
    
    print("请求数据格式:")
    import json
    print(json.dumps(request_data, ensure_ascii=False, indent=2))


def test_file_upload_dry_run():
    """测试文件上传（不实际发送请求）"""
    print("\n=== 文件上传测试（模拟） ===")
    
    test_file = create_test_file()
    
    print(f"准备上传文件: {test_file}")
    print("注意: 这是模拟测试，不会实际发送网络请求")
    
    # 这里可以添加实际的上传测试
    # 但需要确保API服务可用
    print("如需实际测试，请运行: python upload_example.py")


def main():
    """主测试函数"""
    print("=== 文件上传功能测试 ===")
    
    try:
        test_md5_calculation()
        test_api_request_format()
        test_file_upload_dry_run()
        
        print("\n✅ 所有测试完成")
        print("\n下一步:")
        print("1. 将实际文件放入data目录")
        print("2. 运行: python upload_example.py")
        print("3. 或直接调用: python upload_example.py 'your_file.pdf'")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
