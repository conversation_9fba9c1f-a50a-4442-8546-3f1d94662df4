#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传示例
演示如何使用FileUploader上传文件
"""

import os
import sys
from file_upload import FileUploader, upload_file_from_data_dir


def main():
    """主函数"""
    print("=== 文件上传工具 ===")
    
    # 检查data目录
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"已创建 {data_dir} 目录")
    
    # 列出可用文件
    files = [f for f in os.listdir(data_dir) if os.path.isfile(os.path.join(data_dir, f))]
    
    if not files:
        print(f"错误: {data_dir} 目录中没有文件")
        print(f"请将要上传的文件放入 {data_dir} 目录中")
        return
    
    print(f"\n{data_dir} 目录中的文件:")
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(data_dir, filename)
        file_size = os.path.getsize(file_path)
        print(f"{i}. {filename} ({file_size} bytes)")
    
    # 选择文件
    if len(sys.argv) > 1:
        # 从命令行参数获取文件名
        filename = sys.argv[1]
        if filename not in files:
            print(f"错误: 文件 {filename} 不存在于 {data_dir} 目录中")
            return
    else:
        # 交互式选择
        try:
            choice = input(f"\n请选择要上传的文件 (1-{len(files)}): ")
            index = int(choice) - 1
            if index < 0 or index >= len(files):
                print("错误: 无效的选择")
                return
            filename = files[index]
        except (ValueError, KeyboardInterrupt):
            print("\n操作已取消")
            return
    
    print(f"\n选择的文件: {filename}")
    
    # 配置上传选项
    convert_options = {
        'convert_to_pdf': False,
        'convert_to_html': False,
        'convert_to_ofd': True  # 默认转换为OFD
    }
    
    # 根据文件类型调整转换选项
    file_ext = os.path.splitext(filename)[1].lower()
    if file_ext in ['.doc', '.docx']:
        print("检测到Word文档，启用OFD转换")
        convert_options['convert_to_ofd'] = True
    elif file_ext == '.pdf':
        print("检测到PDF文档")
        convert_options['convert_to_ofd'] = False
    
    try:
        print(f"\n开始上传文件: {filename}")
        print(f"转换选项: {convert_options}")
        
        # 执行上传
        file_id = upload_file_from_data_dir(filename, **convert_options)
        
        print(f"\n✅ 上传成功!")
        print(f"文件ID: {file_id}")
        print(f"文件名: {filename}")
        
        # 保存结果到文件
        result_file = "upload_results.txt"
        with open(result_file, "a", encoding="utf-8") as f:
            f.write(f"{filename} -> {file_id}\n")
        print(f"结果已保存到: {result_file}")
        
    except Exception as e:
        print(f"\n❌ 上传失败: {e}")
        return 1
    
    return 0


def upload_specific_file(filename: str, **kwargs):
    """
    上传指定文件的便捷函数
    
    Args:
        filename: 文件名
        **kwargs: 上传选项
    
    Returns:
        文件ID
    """
    try:
        file_id = upload_file_from_data_dir(filename, **kwargs)
        print(f"文件 {filename} 上传成功，文件ID: {file_id}")
        return file_id
    except Exception as e:
        print(f"文件 {filename} 上传失败: {e}")
        raise


if __name__ == "__main__":
    # 示例用法:
    # python upload_example.py                    # 交互式选择文件
    # python upload_example.py "文档.docx"        # 直接指定文件名
    
    exit_code = main()
    sys.exit(exit_code)
