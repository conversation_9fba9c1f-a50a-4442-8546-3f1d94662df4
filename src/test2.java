package com.alibaba.chaosblade.box.web.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * 引发FullGC
 *
 * <AUTHOR>
 */
public class ChaosController {
    private static long counter = 0;

    private static List<Object> list = new ArrayList<>();

    public Object run(Map<String, Object> params) {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        for (int i = 0; i < availableProcessors; i++) {
            new Thread(() -> {
                while (true) {
                    list.add(Person.newInstance("ningque", 29, "天谷"));
                    list.add(Person.newInstance("xuannv", 28, "天谷"));
                    counter += 2;
                    System.out.println("counter : " + counter);
                }
            }).start();
        }
        return null;
    }

}

class Person {

    private String name;
    private Integer age;
    private String address;
    private String name2;
    private Integer age2;
    private String address2;

    private Person(String name, Integer age, String address) {
        this.name = name;
        this.age = age;
        this.address = address;
    }

    static Object newInstance(String name, Integer age, String address) {
        Person person = new Person(name, age, address);
        person.name2 = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
        person.age2 = 33333;
        person.address2 = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
        return person;
    }

    @Override
    public String toString() {
        return "Person{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", address='" + address + '\'' +
                ", name2='" + name2 + '\'' +
                ", age2=" + age2 +
                ", address2='" + address2 + '\'' +
                '}';
    }
}
