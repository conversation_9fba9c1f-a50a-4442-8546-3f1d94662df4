// ChaosBlade JVM Script - Sleep 15 seconds
// 用于container-jvm.script的Java延迟脚本

import java.lang.Thread;
import java.util.concurrent.TimeUnit;

public class Sleep15 {
    
    /**
     * 执行15秒延迟
     * 这个方法会被ChaosBlade注入到目标方法中
     */
    public static void execute() {
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 输出延迟开始信息（可选，用于调试）
            System.out.println("[ChaosBlade] Starting 15 seconds delay at: " + 
                new java.util.Date(startTime));
            
            // 执行15秒延迟
            Thread.sleep(15000);
            
            // 记录结束时间
            long endTime = System.currentTimeMillis();
            
            // 输出延迟结束信息（可选，用于调试）
            System.out.println("[ChaosBlade] Finished 15 seconds delay at: " + 
                new java.util.Date(endTime) + 
                " (actual duration: " + (endTime - startTime) + "ms)");
                
        } catch (InterruptedException e) {
            // 处理中断异常
            Thread.currentThread().interrupt();
            System.err.println("[ChaosBlade] Sleep interrupted: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他异常
            System.err.println("[ChaosBlade] Error during sleep: " + e.getMessage());
        }
    }
    
    /**
     * 带参数的延迟方法
     * @param milliseconds 延迟时间（毫秒）
     */
    public static void executeWithDelay(long milliseconds) {
        try {
            long startTime = System.currentTimeMillis();
            
            System.out.println("[ChaosBlade] Starting " + milliseconds + "ms delay at: " + 
                new java.util.Date(startTime));
            
            Thread.sleep(milliseconds);
            
            long endTime = System.currentTimeMillis();
            System.out.println("[ChaosBlade] Finished delay at: " + 
                new java.util.Date(endTime) + 
                " (actual duration: " + (endTime - startTime) + "ms)");
                
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("[ChaosBlade] Sleep interrupted: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("[ChaosBlade] Error during sleep: " + e.getMessage());
        }
    }
    
    /**
     * 随机延迟方法
     * 在指定范围内随机延迟
     */
    public static void executeRandomDelay() {
        try {
            // 10-20秒之间的随机延迟
            long randomDelay = 10000 + (long) (Math.random() * 10000);
            
            long startTime = System.currentTimeMillis();
            
            System.out.println("[ChaosBlade] Starting random delay of " + randomDelay + "ms at: " + 
                new java.util.Date(startTime));
            
            Thread.sleep(randomDelay);
            
            long endTime = System.currentTimeMillis();
            System.out.println("[ChaosBlade] Finished random delay at: " + 
                new java.util.Date(endTime) + 
                " (actual duration: " + (endTime - startTime) + "ms)");
                
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("[ChaosBlade] Sleep interrupted: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("[ChaosBlade] Error during sleep: " + e.getMessage());
        }
    }
    
    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        System.out.println("Testing ChaosBlade Sleep Script...");
        
        if (args.length > 0) {
            try {
                long delay = Long.parseLong(args[0]);
                executeWithDelay(delay);
            } catch (NumberFormatException e) {
                System.err.println("Invalid delay time: " + args[0]);
                execute(); // 默认执行15秒延迟
            }
        } else {
            execute(); // 默认执行15秒延迟
        }
        
        System.out.println("Test completed.");
    }
}
