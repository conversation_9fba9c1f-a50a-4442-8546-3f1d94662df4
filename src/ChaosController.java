package com.timevale.saasbiz.chaosblade;

import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.common.response.GetAccessRecordResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * AccessRecord 故障注入脚本
 * 用于 ChaosBlade container-jvm.script 故障注入
 */
public class AccessRecordFaultInjection {

    /**
     * 注入到 AccessRecordServiceImpl.getBeforeUrl 方法
     * 使其抛出异常，触发全局异常处理
     */
    public static String injectGetBeforeUrlFault(String accountId) {
        System.out.println("[ChaosBlade] 故障注入生效 - getBeforeUrl方法被调用，accountId: " + accountId);

        // 抛出运行时异常，包含特定标识
        throw new RuntimeException("FAULT_INJECTION_401_LOGIN_TIMEOUT");
    }

    /**
     * 注入到 AccessRecordRest.getBeforeUrl 方法
     * 直接返回 401 错误响应
     */
    public static RestResult<GetAccessRecordResponse> injectRestGetBeforeUrlFault(String accountId) {
        System.out.println("[ChaosBlade] REST层故障注入生效 - getBeforeUrl方法被调用");

        // 创建包含登录URL的数据对象
        Map<String, Object> data = new HashMap<>();
        data.put("loginUrl", "https://testfront.tsign.cn:8887");
        data.put("data", null);

        // 返回401错误响应
        RestResult<GetAccessRecordResponse> result = RestResult.fail(401, "登录超时");
        // 注意：RestResult.fail 方法可能不支持设置 data，需要根据实际情况调整

        return result;
    }

    /**
     * 创建自定义异常，包含特定的错误信息
     */
    public static class LoginTimeoutException extends RuntimeException {
        private final int code;
        private final Map<String, Object> data;

        public LoginTimeoutException() {
            super("登录超时");
            this.code = 401;
            this.data = new HashMap<>();
            this.data.put("loginUrl", "https://testfront.tsign.cn:8887");
            this.data.put("data", null);
        }

        public int getCode() {
            return code;
        }

        public Map<String, Object> getData() {
            return data;
        }
    }

    /**
     * 抛出自定义异常的故障注入方法
     */
    public static String injectWithCustomException(String accountId) {
        System.out.println("[ChaosBlade] 自定义异常故障注入生效");
        throw new LoginTimeoutException();
    }
}