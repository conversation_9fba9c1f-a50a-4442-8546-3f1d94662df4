#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传工具
实现获取上传地址和上传文件流的完整流程
"""

import os
import hashlib
import base64
import mimetypes
import requests
import json
from typing import Optional, Dict, Any


class FileUploader:
    """文件上传器"""
    
    def __init__(self, base_url: str = "http://in-test-openapi.tsign.cn"):
        self.base_url = base_url
        self.headers = {
            'X-Tsign-Open-Auth-Mode': 'simple',
            'X-Tsign-Open-App-Id': '7876676154',
            'X-Tsign-service-group': 'ofd-sign-project',
            'Content-Type': 'application/json'
        }
    
    def calculate_md5(self, file_path: str) -> str:
        """计算文件MD5值并返回base64编码"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return base64.b64encode(hash_md5.digest()).decode('utf-8')
    
    def get_file_size(self, file_path: str) -> int:
        """获取文件大小"""
        return os.path.getsize(file_path)
    
    def get_content_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'
    
    def get_upload_url(self, file_path: str, convert_to_pdf: bool = False, 
                      convert_to_html: bool = False, convert_to_ofd: bool = True) -> Dict[str, Any]:
        """
        获取文件上传地址
        
        Args:
            file_path: 文件路径
            convert_to_pdf: 是否转换为PDF
            convert_to_html: 是否转换为HTML
            convert_to_ofd: 是否转换为OFD
            
        Returns:
            包含fileId和fileUploadUrl的字典
        """
        # 计算文件信息
        file_name = os.path.basename(file_path)
        file_size = self.get_file_size(file_path)
        content_md5 = self.calculate_md5(file_path)
        content_type = self.get_content_type(file_path)
        
        # 构建请求数据
        request_data = {
            "contentMd5": content_md5,
            "contentType": content_type,
            "convertToPDF": convert_to_pdf,
            "convertToHTML": convert_to_html,
            "fileName": file_name,
            "fileSize": file_size,
            "convertToOFD": convert_to_ofd
        }
        
        # 发送请求
        url = f"{self.base_url}/v3/files/file-upload-url"
        
        print(f"请求获取上传地址...")
        print(f"URL: {url}")
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, headers=self.headers, json=request_data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code != 200:
            raise Exception(f"获取上传地址失败: {response.status_code} - {response.text}")
        
        result = response.json()
        if result.get('code') != 0:
            raise Exception(f"获取上传地址失败: {result.get('message', '未知错误')}")
        
        return result['data']
    
    def upload_file_stream(self, file_path: str, upload_url: str) -> Dict[str, Any]:
        """
        上传文件流
        
        Args:
            file_path: 文件路径
            upload_url: 上传地址
            
        Returns:
            上传结果
        """
        # 计算文件MD5用于Content-MD5头
        content_md5 = self.calculate_md5(file_path)
        content_type = self.get_content_type(file_path)
        
        # 构建上传头信息
        upload_headers = {
            'Content-MD5': content_md5,
            'Content-Type': content_type
        }
        
        print(f"开始上传文件...")
        print(f"上传URL: {upload_url}")
        print(f"文件路径: {file_path}")
        print(f"上传头信息: {upload_headers}")
        
        # 读取文件并上传
        with open(file_path, 'rb') as file_data:
            response = requests.put(upload_url, headers=upload_headers, data=file_data)
        
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text}")
        
        if response.status_code not in [200, 201, 204]:
            raise Exception(f"文件上传失败: {response.status_code} - {response.text}")
        
        # 解析响应
        try:
            result = response.json()
            return result
        except json.JSONDecodeError:
            # 某些情况下可能返回空响应或非JSON响应
            return {"errCode": 0, "msg": "成功"}
    
    def upload_file(self, file_path: str, convert_to_pdf: bool = False, 
                   convert_to_html: bool = False, convert_to_ofd: bool = True) -> str:
        """
        完整的文件上传流程
        
        Args:
            file_path: 文件路径
            convert_to_pdf: 是否转换为PDF
            convert_to_html: 是否转换为HTML
            convert_to_ofd: 是否转换为OFD
            
        Returns:
            文件ID
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        print(f"开始上传文件: {file_path}")
        
        # 步骤1: 获取上传地址
        upload_info = self.get_upload_url(file_path, convert_to_pdf, convert_to_html, convert_to_ofd)
        file_id = upload_info['fileId']
        upload_url = upload_info['fileUploadUrl']
        
        print(f"获取到文件ID: {file_id}")
        
        # 步骤2: 上传文件流
        upload_result = self.upload_file_stream(file_path, upload_url)
        
        if upload_result.get('errCode') == 0:
            print(f"文件上传成功! 文件ID: {file_id}")
            return file_id
        else:
            raise Exception(f"文件上传失败: {upload_result}")


def upload_file_from_data_dir(filename: str, **kwargs) -> str:
    """
    从data目录上传指定文件
    
    Args:
        filename: 文件名
        **kwargs: 其他上传参数
        
    Returns:
        文件ID
    """
    # 构建文件路径
    data_dir = "data"
    file_path = os.path.join(data_dir, filename)
    
    # 创建上传器并上传
    uploader = FileUploader()
    return uploader.upload_file(file_path, **kwargs)


if __name__ == "__main__":
    # 示例用法
    try:
        # 确保data目录存在
        if not os.path.exists("data"):
            os.makedirs("data")
            print("已创建data目录，请将要上传的文件放入该目录")
        
        # 列出data目录中的文件
        data_files = [f for f in os.listdir("data") if os.path.isfile(os.path.join("data", f))]
        
        if not data_files:
            print("data目录中没有文件，请先放入要上传的文件")
        else:
            print("data目录中的文件:")
            for i, file in enumerate(data_files, 1):
                print(f"{i}. {file}")
            
            # 可以在这里指定要上传的文件
            # file_id = upload_file_from_data_dir("your_file.pdf")
            # print(f"上传完成，文件ID: {file_id}")
            
    except Exception as e:
        print(f"错误: {e}")
