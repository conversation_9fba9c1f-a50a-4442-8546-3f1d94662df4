# 文件上传工具

这是一个Python实现的文件上传工具，用于上传文件到指定的API服务。

## 功能特性

- 自动计算文件MD5值和大小
- 支持多种文件类型的MIME类型检测
- 完整的两步上传流程：获取上传地址 → 上传文件流
- 支持文件格式转换选项（PDF、HTML、OFD）
- 详细的日志输出和错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备文件

将要上传的文件放入 `data` 目录中：

```bash
mkdir -p data
cp /path/to/your/file.pdf data/
```

### 2. 基本使用

#### 交互式上传
```bash
python upload_example.py
```

#### 直接指定文件
```bash
python upload_example.py "文档.pdf"
```

### 3. 编程方式使用

```python
from file_upload import FileUploader, upload_file_from_data_dir

# 方式1: 使用便捷函数
file_id = upload_file_from_data_dir("文档.pdf", convert_to_ofd=True)
print(f"文件ID: {file_id}")

# 方式2: 使用FileUploader类
uploader = FileUploader()
file_id = uploader.upload_file("data/文档.pdf", convert_to_ofd=True)
print(f"文件ID: {file_id}")
```

## API接口说明

### 1. 获取文件上传地址接口

**请求地址**: `POST /v3/files/file-upload-url`

**请求头**:
- `X-Tsign-Open-Auth-Mode: simple`
- `X-Tsign-Open-App-Id: 7876676154`
- `X-Tsign-service-group: ofd-sign-project`
- `Content-Type: application/json`

**请求参数**:
```json
{
    "contentMd5": "文件MD5值(base64编码)",
    "contentType": "文件MIME类型",
    "convertToPDF": false,
    "convertToHTML": false,
    "fileName": "文件名",
    "fileSize": 文件大小,
    "convertToOFD": true
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "成功",
    "data": {
        "fileId": "文件ID",
        "fileUploadUrl": "上传地址"
    }
}
```

### 2. 上传文件流接口

**请求方式**: `PUT {fileUploadUrl}`

**请求头**:
- `Content-MD5: 文件MD5值(base64编码)`
- `Content-Type: 文件MIME类型`

**请求体**: 文件二进制数据

**响应示例**:
```json
{
    "errCode": 0,
    "msg": "成功"
}
```

## 配置说明

### 转换选项

- `convert_to_pdf`: 是否转换为PDF格式
- `convert_to_html`: 是否转换为HTML格式
- `convert_to_ofd`: 是否转换为OFD格式

### 支持的文件类型

工具会自动检测文件的MIME类型，支持常见的文档格式：
- PDF文档
- Word文档 (.doc, .docx)
- Excel文档 (.xls, .xlsx)
- PowerPoint文档 (.ppt, .pptx)
- 图片文件 (.jpg, .png, .gif等)
- 其他文件类型

## 错误处理

程序包含完整的错误处理机制：
- 文件不存在检查
- 网络请求异常处理
- API响应错误处理
- 详细的错误信息输出

## 输出文件

- `upload_results.txt`: 记录上传成功的文件名和对应的文件ID

## 注意事项

1. 确保网络连接正常
2. 确保API服务可访问
3. 文件大小限制请参考API文档
4. MD5计算可能需要一些时间，特别是对于大文件

## 示例

```bash
# 上传Word文档并转换为OFD
python upload_example.py "合同.docx"

# 上传PDF文档
python upload_example.py "报告.pdf"
```
